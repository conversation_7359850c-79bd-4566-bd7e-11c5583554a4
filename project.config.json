{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "srcMiniprogramRoot": "miniprogram/", "setting": {"ignoreUploadUnusedFiles": true, "ignoreDevUnusedFiles": true, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "enhance": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "minified": true, "minifyWXSS": true, "minifyWXML": true, "newFeature": false, "uglifyFileName": true, "uploadWithSourceMap": true, "useIsolateContext": true, "useMultiFrameRuntime": true, "nodeModules": false, "showShadowRootInWxmlPanel": true, "enableEngineNative": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "showES6CompileOption": false, "babelSetting": {"ignore": ["./miniprogram/miniprogram_npm/*", "./miniprogram/library/extend/*", "./miniprogram/library/optimizer/*"], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "useCompilerPlugins": ["sass"], "condition": true}, "compileType": "miniprogram", "packOptions": {"ignore": [{"value": "\\.md$", "type": "regexp"}, {"value": "node_modules", "type": "folder"}], "include": []}, "appid": "wxc095cf3af23776fc", "scripts": {"beforePreview": "source $HOME/.zshrc;npm run switch:tsl", "beforeUpload": "source $HOME/.zshrc;npm run switch:tsl", "beforeCompile": "source $HOME/.zshrc;npm run switch:tsl"}, "projectname": "tianneng-advertising-applet", "libVersion": "3.0.2", "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 4}}