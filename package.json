{"name": "tianneng-advertising-applet", "version": "1.0.0", "description": "天能广告微信小程序", "scripts": {"prepare": "husky", "lint-staged": "lint-staged", "eslint": "eslint . --ext .js,.wxs", "eslint:fix": "eslint --fix . --ext .js,.wxs", "prettier:fix": "prettier --config .prettierrc.yml --write ./**/*.{js,wxs,css,wxss,wxml,less,scss,json}", "env:dev": "node miniprogram/script.env.js --dev", "env:tsl": "node miniprogram/script.env.js --tsl", "env:prod": "node miniprogram/script.env.js --prod", "switch:dev": "npm-run-all -s env:dev eslint:fix prettier:fix", "switch:tsl": "npm-run-all -s env:tsl eslint:fix prettier:fix", "switch:prod": "npm-run-all -s env:prod eslint:fix prettier:fix"}, "keywords": [], "author": "tench", "dependencies": {"fast-xml-parser": "^4.4.1", "form-data": "^4.0.0", "mini-stores": "^2.0.0", "tdesign-miniprogram": "^1.4.4", "timer-miniprogram": "^0.0.1"}, "devDependencies": {"babel-eslint": "10.0.3", "eslint": "6.7.1", "eslint-config-alloy": "3.7.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.4", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all": "4.1.5", "prettier": "^2.2.0", "prettier-eslint-cli": "5.0.0"}, "main": ".eslintrc.js", "license": "ISC"}