const config = require('../../config/index');

const { XMLParser } = require('fast-xml-parser');

import { POST } from '../../library/optimizer/request';

Page({
  data: {
    gridConfig: {
      column: 3,
      width: 160,
      height: 160,
    },
    address: '',
    adCompany: '',
    adCompanyCode: '',
    shop: '',
    shopCode: '',
    phone: '',
    longitude: '',
    latitude: '',
    province: '',
    city: '',
    district: '',
    isLocating: false, // 是否正在定位
    adPositionOptions: [
      {
        label: '门头-门店正上方',
        value: '门头-门店正上方',
      },
      {
        label: '其他-门店内部',
        value: '其他-门店内部',
      },
      {
        label: '其他-门店门口',
        value: '其他-门店门口',
      },
      {
        label: '其他-门店周边',
        value: '其他-门店周边',
      },
      {
        label: '车贴-车身',
        value: '车贴-车身',
      },
      {
        label: '其他-备注注明',
        value: '其他-备注注明',
      },
    ],
    activityTypeText: '',
    activityTypeValue: '', // 默认选择第一个选项
    activityTypeVisible: false,
    activityTypes: [],
    activityIndex: 0,
    accountCode: '',
    isActApply: '',
    quantity: 1,
    theOlnyCodeForOne: '',
    doors: [
      {
        photos: [],
        video: [],
        adType: 1,
        adPositionValue: '', // 默认选择第一个选项
        adPositionVisible: false,
        addressType: '现场选址',
        adAddress: '现场选址',
      },
    ],
    menTouNum: 0,
    notMenTouNum: 0,
    countFlag: false,
  },

  onLoad() {
    this.getLocation();
    this.getActivityTypes();
    // 获取当前时间戳
    const currentTimeMillis = Date.now();
    // 生成唯一字符串
    const uniqueString = this.generateUUID() + currentTimeMillis;
    this.setData({
      theOlnyCodeForOne: uniqueString,
    });
  },

  // 生成UUID
  generateUUID() {
    let d = Date.now();
    if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
      d += performance.now(); // 使用高精度时间
    }
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
  },

  getActCostAccounts(actType) {
    const params = {
      actType: actType,
    };

    POST(`/apiTtDealerActApplyController.do?getActCodeByCustomerInfo`, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        const actCode = res.actCode;
        if (actCode) {
          this.setData({
            actCode: actCode,
          });
        } else {
          wx.showToast({
            title: '查询失败',
            icon: 'none',
          });
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.head ? err.head.message : '查询失败',
          icon: 'none',
        });
      });
  },

  // 获取剩余门头/非门头额度
  getCounts() {
    wx.showLoading({
      title: '加载中...',
    });

    const params = {};

    POST(`/tsActAddressAndExecuteController.do?findCanSubmitCount&teboShopId=` + this.data.shopCode, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.hideLoading();
        const res1 = res.filter((item) => {
          return item.adType == 1;
        });
        const res2 = res.filter((item) => {
          return item.adType == 2;
        });
        this.setData({
          menTouNum: res1[0].adCount,
          notMenTouNum: res2[0].adCount,
          countFlag: true,
        });
      })
      .catch((err) => {
        wx.hideLoading();
        wx.showToast({
          title: err.head ? err.head.message : '加载失败',
          icon: 'none',
        });
      });
  },

  // 获取活动类型
  getActivityTypes() {
    wx.showLoading({
      title: '加载中...',
    });

    const params = {};

    POST(`/apiSfaTtCostAccountController.do?getActCostAccounts`, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.hideLoading();
        const activityTypes = res.map((item) => ({
          label: item.accountName,
          value: item,
        }));
        if (activityTypes.length == 1) {
          this.setData({
            activityTypeText: activityTypes[0].value.accountName,
            activityTypeValue: activityTypes[0].value.accountName,
            accountCode: activityTypes[0].value.accountCode,
            isActApply: activityTypes[0].value.isActApply,
          });
          this.getActCostAccounts(activityTypes[0].value.accountCode);
        }

        this.setData({
          activityTypes,
        });
      })
      .catch((err) => {
        wx.hideLoading();
        wx.showToast({
          title: err.head ? err.head.message : '加载失败',
          icon: 'none',
        });
      });
  },

  // 打开广告位置选择器
  onAdPositionPicker(e) {
    const { index } = e.currentTarget.dataset;
    const doors = this.data.doors;
    doors[index].adPositionVisible = true;
    this.setData({
      doors,
    });
  },

  // 打开活动类型选择器
  onActivityTypePicker() {
    this.setData({
      activityTypeVisible: true,
    });
  },

  // 处理广告位置选择结果
  onPickerChange(event) {
    const { key, index } = event.currentTarget.dataset;
    const { value } = event.detail;
    const doors = this.data.doors;
    if (key === 'adPosition') {
      doors[index].adPositionValue = value[0];
      doors[index].adPositionVisible = false;
      this.setData({
        doors,
      });
    } else if (key === 'activityType') {
      this.setData({
        activityTypeText: value[0].accountName,
        activityTypeValue: value[0].accountName,
        activityTypeVisible: false,
        accountCode: value[0].accountCode,
        isActApply: value[0].isActApply,
      });
      this.getActCostAccounts(value[0].accountCode);
    }
  },

  // 处理广告位置选择取消
  onPickerCancel(event) {
    const { index } = event.currentTarget.dataset;
    const doors = this.data.doors;
    doors[index].adPositionVisible = false;
    this.setData({
      doors,
    });
    this.setData({
      activityTypeVisible: false,
    });
  },

  onAdTypeRadioChange(e) {
    const { value } = e.detail;
    const { index } = e.currentTarget.dataset;
    console.log('value', value);
    console.log('index', index);
    const doors = this.data.doors;
    // 根据选择的类型更新地址文本框的默认值
    if (value === '门头') {
      doors[index].adType = 1;
    } else {
      doors[index].adType = 2;
    }

    // 更新选中的地址类型
    // doors[index].adType = value;

    this.setData({
      doors,
    });
  },

  onRadioChange(e) {
    const { value } = e.detail;
    const { index } = e.currentTarget.dataset;
    const doors = this.data.doors;

    // 根据选择的类型更新地址文本框的默认值
    if (value === '现场选址') {
      doors[index].adAddress = '现场选址';
    } else {
      doors[index].adAddress = '';
    }

    // 更新选中的地址类型
    doors[index].addressType = value;

    this.setData({
      doors,
    });
  },

  // 处理广告位置选择器列改变
  onColumnChange(event) {
    // 如果需要处理列改变事件，可以在这里添加逻辑
  },

  // 获取用户当前位置
  getLocation() {
    console.log('开始获取当前位置');

    // 设置定位状态
    this.setData({
      isLocating: true
    });

    // 设置超时处理
    const timeoutId = setTimeout(() => {
      console.log('定位超时');
      this.setData({
        isLocating: false
      });
      wx.showToast({
        title: '定位超时，请重试',
        icon: 'none',
      });
    }, 10000); // 10秒超时

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        clearTimeout(timeoutId);
        console.log('定位成功:', res);
        this.setData({
          isLocating: false
        });
        const { latitude, longitude } = res;
        this.reverseGeocode(latitude, longitude);
      },
      fail: (err) => {
        clearTimeout(timeoutId);
        console.log('定位失败:', err);
        this.setData({
          isLocating: false
        });

        if (err.errMsg === 'getLocation:fail auth deny') {
          wx.showModal({
            title: '权限请求',
            content: '需要获取您的位置信息，请到设置页面开启权限。',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 用户重新授权成功，调用获取位置方法
                      this.getLocation();
                    } else {
                      wx.showToast({
                        title: '未授权位置信息，无法使用该功能',
                        icon: 'none',
                      });
                    }
                  },
                });
              }
            },
          });
        } else {
          wx.showToast({
            title: '获取位置失败: ' + err.errMsg,
            icon: 'none',
            duration: 3000
          });
        }
      },
    });
  },

  reLocate: function () {
    this.getLocation();
  },

  // 地理位置反编码获取地址
  reverseGeocode(latitude, longitude) {
    console.log('开始地址解析:', latitude, longitude);

    const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${config.baiduMapKey}&output=json&coordtype=wgs84ll&location=${latitude},${longitude}`;
    wx.request({
      url,
      success: (res) => {
        console.log('地址解析响应:', res);
        if (res.data && res.data.result && res.data.result.formatted_address) {
          const addressComponent = res.data.result.addressComponent;
          this.setData({
            latitude: `${latitude}`,
            longitude: `${longitude}`,
            address: res.data.result.formatted_address,
            province: addressComponent.province,
            city: addressComponent.city,
            district: addressComponent.district,
            isLocating: false // 确保关闭定位状态
          });
          console.log('地址解析完成:', res.data.result.formatted_address);
        } else {
          console.log('地址解析失败，数据格式异常');
          this.setData({
            isLocating: false
          });
          wx.showToast({
            title: '地址解析失败',
            icon: 'none',
          });
        }
      },
      fail: (err) => {
        console.log('地址解析请求失败:', err);
        this.setData({
          isLocating: false
        });
        wx.showToast({
          title: '获取地址失败',
          icon: 'none',
        });
      },
    });
  },

  // 导航到广告公司选择页面
  navigateToAdCompanyPicker() {
    wx.navigateTo({
      url: '/pages/adCompanyPicker/index?mode=select',
    });
  },

  // 导航到安装门头店铺选择页面
  navigateToShopPicker() {
    wx.navigateTo({
      url: '/pages/shopPicker/index?mode=select',
    });
  },
  onShow() {
    const selectedAdCompany = wx.getStorageSync('selectedAdCompany');
    if (selectedAdCompany) {
      // 格式化显示为：名称（手机号）
      const displayText = `${selectedAdCompany.fullName || ''}（${selectedAdCompany.userName || ''}）`;
      this.setData({
        adCompany: displayText,
        adCompanyCode: selectedAdCompany.userName || '',
      });
      // 清除缓存，以防再次进入时数据干扰
      wx.removeStorageSync('selectedAdCompany');
    }
    const selectedTerminal = wx.getStorageSync('selectedTerminal');
    if (selectedTerminal) {
      // 格式化显示为：名称（手机号）
      const displayText = `${selectedTerminal.shopName || ''}（${selectedTerminal.phoneNumber || ''}）`;
      this.setData({
        shop: displayText,
        shopCode: selectedTerminal.id || '',
        phone: selectedTerminal.phoneNumber || '',
      });
      this.getCounts();
      // 清除缓存，以防再次进入时数据干扰
      wx.removeStorageSync('selectedTerminal');
    }
  },

  // 广告发布地址输入
  onAdAddressInput(e) {
    const { index } = e.currentTarget.dataset;
    const doors = this.data.doors;
    doors[index].adAddress = e.detail.value;
    this.setData({
      doors,
    });
  },
  onLineChange(e) {
    console.log('lineCount: ', e.detail);
  },

  // 活动类型选择
  onActivityChange(e) {
    this.setData({
      activityIndex: e.detail.value,
    });
  },
  // 输入框事件
  onInput(e) {
    const { field, index } = e.currentTarget.dataset;
    const doors = this.data.doors;
    doors[index][field] = e.detail.value;
    this.setData({
      doors,
    });
  },

  validateForm() {
    const { address, adCompany, shop, activityTypeValue, doors } = this.data;
    if (!address) {
      wx.showToast({
        title: '请获取当前定位',
        icon: 'none',
      });
      return false;
    }
    if (!adCompany) {
      wx.showToast({
        title: '请选择广告公司',
        icon: 'none',
      });
      return false;
    }

    if (!shop) {
      wx.showToast({
        title: '请选择要安装门头的店铺',
        icon: 'none',
      });
      return false;
    }

    if (!activityTypeValue) {
      wx.showToast({
        title: '请选择活动类型',
        icon: 'none',
      });
      return false;
    }

    if (doors.length === 0) {
      wx.showToast({
        title: '请添加门头',
        icon: 'none',
      });
      return false;
    } else {
      for (let i = 0; i < doors.length; i++) {
        const { adPositionValue, photos, video, adAddress } = doors[i];

        if (!adPositionValue) {
          wx.showToast({
            title: '请选择广告位置',
            icon: 'none',
          });
          return false;
        }

        if (photos.length === 0) {
          wx.showToast({
            title: '请上传门头照片',
            icon: 'none',
          });
          return false;
        }

        if (video.length === 0) {
          wx.showToast({
            title: '请上传门头视频',
            icon: 'none',
          });
          return false;
        }

        for (let j = 0; j < photos.length; j++) {
          if (photos[j].status === 'error') {
            wx.showToast({
              title: '照片上传失败，请重新上传',
              icon: 'none',
            });
            return false;
          }

          if (photos[j].status !== 'done') {
            wx.showToast({
              title: '请等待照片上传完成',
              icon: 'none',
            });
            return false;
          }
        }

        for (let k = 0; k < video.length; k++) {
          if (video[k].status === 'error') {
            wx.showToast({
              title: '视频上传失败，请重新上传',
              icon: 'none',
            });
            return false;
          }

          if (video[k].status !== 'done') {
            wx.showToast({
              title: '请等待视频上传完成',
              icon: 'none',
            });
            return false;
          }
        }

        if (!adAddress) {
          wx.showToast({
            title: '请填写广告发布地址',
            icon: 'none',
          });
          return false;
        }
      }
    }

    return true;
  },

  // 增加门头数量
  increaseQuantity() {
    const doors = this.data.doors;
    if (!this.data.countFlag) {
      wx.showToast({
        title: '请先选择店铺',
        icon: 'none',
      });
      return; // 直接返回，不再增加门头数量
    }
    // 检查当前门头数量是否已达到最大值
    if (doors.length >= this.data.notMenTouNum + this.data.menTouNum) {
      wx.showToast({
        title: '制作数量剩余额度已达上限',
        icon: 'none',
      });
      return; // 直接返回，不再增加门头数量
    }
    doors.push({
      adPosition: '',
      photos: [],
      video: '',
      adType: 1,
      addressType: '现场选址',
      adAddress: '现场选址',
    });
    this.setData({
      quantity: this.data.quantity + 1,
      doors,
    });
  },

  // 减少门头数量
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      const doors = this.data.doors;
      doors.pop();
      this.setData({
        quantity: this.data.quantity - 1,
        doors,
      });
    }
  },

  handleAdd(e) {
    const { index } = e.currentTarget.dataset;
    const { files } = e.detail;
    files.forEach((file) => this.onUpload(file, index));
  },
  handleVideoAdd(e) {
    const { index } = e.currentTarget.dataset;
    const { files } = e.detail;
    files.forEach((file) => this.onUpload(file, index, 'video'));
  },

  onUpload(file, index, type = 'photo') {
    const doors = this.data.doors;
    const { photos, video } = this.data.doors[index];
    const fileIndex = type === 'photo' ? photos.length : video.length;
    const newFile = {
      ...file,
      status: 'loading',
    };

    if (type === 'photo') {
      doors[index].photos = [...photos, newFile];
    } else {
      doors[index].video = [...video, newFile];
    }

    this.setData({
      doors,
    });

    // 获取当前用户的用户名
    const userName = wx.getStorageSync('userData')?.businessObject?.frUserName;
    // 获取当前时间戳
    const timestamp = Date.now();
    // 生成新的文件名
    const newFileName = `${userName}_${timestamp}.${type === 'photo' ? 'jpg' : 'mp4'}`;

    // 获取文件系统管理器
    const fileSystemManager = wx.getFileSystemManager();

    // 压缩文件
    const compressFile = () => {
      if (type === 'photo') {
        console.log('photo');
        return new Promise((resolve, reject) => {
          wx.compressImage({
            src: file.url,
            quality: 80, // 压缩质量
            success: resolve,
            fail: reject,
          });
        });
      } else {
        console.log('video');
        return new Promise((resolve, reject) => {
          wx.compressVideo({
            src: file.url,
            quality: 'high', // 压缩质量，可以是 'low', 'medium', 'high'
            success: resolve,
            fail: reject,
          });
        });
      }
    };

    // 压缩文件并上传
    compressFile()
      .then((compressedFile) => {
        // 新文件路径
        const newFilePath = `${wx.env.USER_DATA_PATH}/${newFileName}`;
        // 复制并重命名压缩后的文件
        fileSystemManager.copyFile({
          srcPath: compressedFile.tempFilePath,
          destPath: newFilePath,
          success: () => {
            // 上传重命名后的文件
            const task = wx.uploadFile({
              url: `${config.defaultURL}/uploadServlet`, // 修改为实际的接口地址
              filePath: newFilePath, // 使用重命名后的文件路径
              name: 'file', // 文件的字段名
              formData: {
                realPath: newFilePath, // 使用重命名后的文件路径作为实际路径
              },
              success: (uploadRes) => {
                // 解析 XML 响应
                const xmlStr = uploadRes.data;
                const parser = new XMLParser();
                const result = parser.parse(xmlStr);
                const returncode = result.response.returncode;
                if (returncode == 100) {
                  // 上传成功，更新文件状态
                  if (type === 'photo') {
                    doors[index].photos[fileIndex].status = 'done';
                    doors[index].photos[fileIndex].url = newFilePath;
                  } else {
                    doors[index].video[fileIndex].status = 'done';
                    doors[index].video[fileIndex].url = newFilePath;
                  }
                  this.setData({
                    doors,
                  });
                } else {
                  // 上传失败，更新文件状态
                  if (type === 'photo') {
                    doors[index].photos[fileIndex].status = 'error';
                  } else {
                    doors[index].video[fileIndex].status = 'error';
                  }
                  this.setData({
                    doors,
                  });
                  console.error('Upload failed with returncode:', returncode);
                }
              },
              fail: (err) => {
                // 上传失败，更新文件状态
                if (type === 'photo') {
                  doors[index].photos[fileIndex].status = 'error';
                } else {
                  doors[index].video[fileIndex].status = 'error';
                }
                this.setData({
                  doors,
                });
                console.error('Upload failed:', err);
              },
            });

            task.onProgressUpdate((res) => {
              if (type === 'photo') {
                doors[index].photos[fileIndex].percent = res.progress;
              } else {
                doors[index].video[fileIndex].percent = res.progress;
              }
              this.setData({
                doors,
              });
            });
          },
          fail: (err) => {
            if (type === 'photo') {
              doors[index].photos[fileIndex].status = 'error';
            } else {
              doors[index].video[fileIndex].status = 'error';
            }
            this.setData({
              doors,
            });
            console.error('Copy file failed:', err);
          },
        });
      })
      .catch((err) => {
        if (type === 'photo') {
          doors[index].photos[fileIndex].status = 'error';
        } else {
          doors[index].video[fileIndex].status = 'error';
        }
        this.setData({
          doors,
        });
        console.error('Compress file failed:', err);
      });
  },

  handleRemove(e) {
    const { index } = e.detail;
    const { index: doorIndex } = e.currentTarget.dataset;
    const { photos } = this.data.doors[doorIndex];
    const doors = this.data.doors;
    photos.splice(index, 1);
    doors[doorIndex].photos = photos;
    this.setData({
      doors,
    });
  },

  handleVideoRemove(e) {
    const { index } = e.detail;
    const { index: doorIndex } = e.currentTarget.dataset;
    const { video } = this.data.doors[doorIndex];
    const doors = this.data.doors;
    video.splice(index, 1);
    doors[doorIndex].video = video;
    this.setData({
      doors,
    });
  },

  // 提交表单
  submit() {
    // 验证表单数据的完整性
    if (!this.validateForm()) {
      return;
    }

    // 获取表单数据
    const address = this.data.address;
    const adCompany = this.data.adCompany;
    const adCompanyCode = this.data.adCompanyCode;
    const shop = this.data.shop;
    const shopCode = this.data.shopCode;
    const phone = this.data.phone;
    const actCode = this.data.actCode;
    const doors = this.data.doors;
    const bussId = wx.getStorageSync('userData')?.businessObject?.frUserName + `${Date.now()}`;
    const userId = wx.getStorageSync('userData')?.businessObject?.userID;

    const formatDate = (date) => {
      const pad = (n) => (n < 10 ? '0' + n : n);
      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(
        date.getMinutes(),
      )}:${pad(date.getSeconds())}`;
    };
    // 构建提交数据对象
    const submitData = {
      businessId: bussId,
      actType: '2', // 门头
      advName: adCompany,
      accountCode: this.data.accountCode,
      realActCode: this.data.isActApply == 1 ? actCode : '',
      advCode: adCompanyCode,
      terminalName: shop,
      terminalCode: shopCode,
      teboShopId: shopCode,
      phone: phone,
      province: this.data.province,
      city: this.data.city,
      area: this.data.district,
      gpsAddress: address,
      longitude: this.data.longitude,
      latitude: this.data.latitude,
      theOlnyCodeForOne: this.data.theOlnyCodeForOne,
      remarks: 'WX',
      detailJson: JSON.stringify(
        doors.map((door) => ({
          place: door.adPositionValue,
          remarks: door.adAddress,
          adType: door.adType,
          picVoList: door.photos
            .map((photo) => ({
              businessId: bussId,
              imgPath: photo.url,
              imgType: '105', // 图片类型
              psTime: formatDate(new Date()),
              uaccount: userId,
            }))
            .concat(
              door.video
                ? [
                  {
                    businessId: bussId,
                    imgPath: door.video[0].url,
                    imgType: '145', // 视频类型
                    psTime: formatDate(new Date()),
                    uaccount: userId,
                  },
                ]
                : [],
            ),
        })),
      ),
    };

    POST(`/tsActAddressAndExecuteController.do?saveTsActApplyAddressSelectOutDoor`, submitData, {
      custom: {
        isLoading: true,
        msg: '保存中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000, // 显示时间
          success: function () {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000); // 等待提示框显示完毕后再返回上一页
          },
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.head ? err.head.message : '提交失败',
          icon: 'none',
        });
      });
  },
});
