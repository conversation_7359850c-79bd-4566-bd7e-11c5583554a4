@import '../miniprogram/resource/scss/index.scss';

/* 页面容器 */
.page {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.container {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #1890ff, #722ed1);
  }
}

/* 定位动画样式 */
.location-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-animation {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 定位中的文字样式 */
.address.locating {
  color: #1890ff;
  font-style: italic;
}

/* 定位按钮旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/* 提示信息区域 */
.tix {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #ffa500 0%, #ff7e00 100%);
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-shadow: 0 8rpx 32rpx rgba(255, 165, 0, 0.15);

  .bef {
    margin-top: 12rpx;
    display: flex;
    align-items: flex-start;

    &::before {
      content: '';
      background-color: #ffffff;
      border-radius: 50%;
      display: inline-block;
      height: 12rpx;
      width: 12rpx;
      margin-right: 16rpx;
      margin-top: 12rpx;
      flex-shrink: 0;
    }
  }
}

.title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 20px;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #1890ff;
    background-color: #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
  }
}

.form-item1 {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  align-items: baseline;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #1890ff;
    background-color: #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
  }
}

/* 标签组 */
.labelGroup {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

.remaining {
  font-size: 28rpx;
  color: red;
  margin-bottom: 10rpx;
}

/* 表单标签 */
.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

/* 标签样式 */
label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.redLab {
  color: #ff4d4f;
  font-weight: 600;
}

.mb-16 {
  display: block;
  margin-bottom: 32rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 自定义选择器 */
.custom-picker {
  width: 100%;
  margin-top: 8rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-height: 88rpx;
  box-sizing: border-box;

  &:active {
    border-color: #1890ff;
    background-color: #f6f9ff;
    transform: scale(0.995);
  }
}

.picker-icon-left {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.picker-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;

  &.placeholder {
    color: #bdbdbd;
  }

  &.selected {
    color: #333;
    font-weight: 500;
  }
}

.picker-arrow {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
  transition: all 0.3s ease;
}

/* 选择器激活状态 */
.custom-picker:active .picker-content {
  border-color: #1890ff;
  background-color: #f6f9ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.custom-picker:active .arrow-icon {
  color: #1890ff;
  transform: scale(1.1);
}

/* 选择器已选中状态 */
.picker-text.selected {
  color: #1890ff;
}

/* 优化t-cell样式 */
:deep(.t-cell) {
  --td-cell-bg-color: transparent;
  --td-cell-border-color: #e0e0e0;
  --td-cell-text-color: #333;
  --td-cell-note-color: #1890ff;
  --td-cell-padding: 24rpx 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  margin-bottom: 0;
}

:deep(.t-cell:active) {
  --td-cell-bg-color: #f6f9ff;
  --td-cell-border-color: #1890ff;
}



/* 选择器包装 */
.picker-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 8rpx;
}

.picker-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
    background-color: rgba(24, 144, 255, 0.2);
  }
}

/* 优化输入框样式 */
:deep(.t-input) {
  --td-input-bg-color: #ffffff;
  --td-input-border-color: #e0e0e0;
  --td-input-border-radius: 12rpx;
  --td-input-placeholder-text-color: #bdbdbd;
  --td-input-text-color: #333;
  --td-input-padding: 24rpx 20rpx;
  --td-input-height: 88rpx;
}

:deep(.t-input:focus) {
  --td-input-border-color: #1890ff;
  --td-input-bg-color: #f6f9ff;
}

/* 区块样式 */
.block {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #52c41a, #1890ff);
  }

  &:hover {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
    border-color: #1890ff;
  }

  &:nth-child(odd) {
    &::before {
      background: linear-gradient(90deg, #1890ff, #722ed1);
    }
  }

  &:nth-child(even) {
    &::before {
      background: linear-gradient(90deg, #52c41a, #13c2c2);
    }
  }
}

.block-content {
  font-size: 14px;
  color: var(--subtle-text-color);
}

.textarea-container {
  margin-top: 10px;
  --td-textarea-placeholder-color: rgb(211, 210, 210);
}

.textarea-label {
  font-size: 12px;
  font-weight: bold;
  color: rgb(185, 34, 34);
  margin-bottom: 5px;
}

.textarea-custom {
  border: 1px solid rgb(104, 103, 103);
  padding: 10px;
  border-radius: 5px;
  font-size: 14px;
  line-height: 1.5;
  color: rgb(0, 0, 0);
  height: 130px;
  --td-textarea-disabled-text-color: rgb(0, 0, 0);
}

/* CSS 样式 */
.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 在一行中对齐标题和控制器 */
  padding: 5px 10px;
  background-color: #fdfbfb; /* 背景颜色稍微区分 */
  border: 1px solid #ddd; /* 边框 */
  border-radius: 10px; /* 圆角 */
  margin-bottom: 10px; /* 下方间距 */
}
.numTitle {
  font-size: 16px;
  color: #333;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.quantity-display {
  margin: 0 5px;
  font-size: 16px;
}

.address {
  flex-grow: 1; /* 占满剩余空间 */
  color: #333; /* 内容颜色 */
  font-size: 14px; /* 内容字号 */
  word-break: break-word; /* 自动换行 */
}

.block-number {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 5px;
}

.title1 {
  margin-top: 10px;
  font-size: 15px;
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
  align-items: baseline; /* 下标对齐 */
}

.titlePart1 {
  font-size: 16px;
  color: #333;
}

.titlePart3 {
  font-size: 14px;
  color: #888888; /* 灰色字体 */
  margin-left: 8px; /* 左侧间距 */
}

.titlePart2 {
  font-size: 14px;
  color: red;
  margin-left: 8px;
}

.radio-group-inline {
  display: flex;
  justify-content: space-between; /* 将两个 radio 项目分布在一行 */
  margin-top: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-item radio {
  margin-right: 5px;
}

/* 选择器动画效果 */
@keyframes pickerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-picker {
  animation: pickerFadeIn 0.3s ease-out;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .picker-content {
    padding: 20rpx 16rpx;
    min-height: 80rpx;
  }

  .picker-icon-left {
    font-size: 28rpx;
    margin-right: 12rpx;
  }

  .picker-text {
    font-size: 26rpx;
  }

  .arrow-icon {
    font-size: 22rpx;
  }

  .picker-icon {
    width: 44rpx;
    height: 44rpx;
    font-size: 26rpx;
  }
}

/* 优化选择器在不同状态下的视觉反馈 */
.custom-picker:active {
  .picker-content {
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
  }
}

/* 选择器禁用状态 */
.custom-picker.disabled {
  .picker-content {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;
  }

  .picker-text {
    color: #bfbfbf;
  }

  .picker-icon-left,
  .arrow-icon {
    color: #bfbfbf;
  }
}
