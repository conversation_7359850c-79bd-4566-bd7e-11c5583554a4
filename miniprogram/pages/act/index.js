const config = require('../../config/index');
import { POST } from '../../library/optimizer/request';
Page({
  data: {
    actList: [], // 用于存储申请列表数据
    searchQuery: '', // 搜索关键词
    page: 1, // 当前页码
    rows: 20, // 每页的数据量
    loading: false, // 避免重复加载
    noMoreData: false, // 是否还有更多数据
    showNoMoreData: false, // 是否还有更多数据
    bpmKey: '',
    bpmStatus: '',
  },

  onLoad(options) {
    // 检查 bpmStatus 是否不为 null 和 undefined
    if (options.bpmStatus !== null && options.bpmStatus !== undefined && 'bpmStatus' in options) {
      this.setData({
        bpmStatus: options.bpmStatus,
      });
    }
    // 检查 bpmKey 是否不为 null 和 undefined
    if (options.bpmKey !== null && options.bpmKey !== undefined && 'bpmKey' in options) {
      this.setData({
        bpmKey: options.bpmKey,
      });
      wx.setNavigationBarTitle({
        title: '6-施工后拍照',
      });
    }
    this.fetchApplyList();
  },

  handleItemTap(event) {
    const act = event.currentTarget.dataset.act;
    const atcStr = encodeURIComponent(JSON.stringify(act));
    if ('11' == act.bpmStatus) {
      wx.navigateTo({
        url: `/pages/submitConPhoto/index?act=${atcStr}`,
      });
    } else {
      wx.navigateTo({
        url: `/pages/postConPhoto/index?act=${atcStr}`,
      });
    }
  },

  onSearchInput: function (e) {
    this.setData({
      searchQuery: e.detail.value,
    });
  },

  onSearch: function () {
    this.setData(
      {
        page: 1,
        actList: [],
        noMoreData: false,
      },
      () => {
        this.fetchApplyList();
      },
    );
  },

  onClear: function () {
    this.setData(
      {
        searchQuery: '',
        page: 1,
        actList: [],
        noMoreData: false,
      },
      () => {
        this.fetchApplyList();
      },
    );
  },

  fetchApplyList() {
    if (this.data.loading || this.data.noMoreData) return;

    this.setData({
      loading: true,
    });
    wx.showLoading({
      title: '加载中...',
    });

    const params = {
      advCode: wx.getStorageSync('userData')?.businessObject?.frUserName,
      actCode: this.data.searchQuery,
      actType: 2,
      bpmKey: this.data.bpmKey,
      bpmStatus: this.data.bpmStatus,
      page: this.data.page,
      rows: this.data.rows,
    };

    POST(`/tsActAddressAndExecuteController.do?findMyTaskList`, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.hideLoading();
        this.setData({
          loading: false,
        });
        const newApplyList = res || [];
        if (newApplyList.length < this.data.rows) {
          this.setData({
            noMoreData: true,
          });
        }
        this.setData({
          actList: this.data.page === 1 ? newApplyList : this.data.applyList.concat(newApplyList),
          page: this.data.page + 1,
        });
      })
      .catch((err) => {
        wx.hideLoading();
        this.setData({
          loading: false,
        });
        wx.showToast({
          title: err.head ? err.head.message : '查询失败',
          icon: 'none',
        });
      });
  },

  onPullDownRefresh: function () {
    this.setData(
      {
        page: 1,
        actList: [],
        noMoreData: false,
      },
      () => {
        this.fetchApplyList();
      },
    );
  },

  onReachBottom() {
    if (this.data.noMoreData) {
      this.setData({
        showNoMoreData: true,
      });
      setTimeout(() => {
        this.setData({
          showNoMoreData: false,
        });
      }, 2000); // 2秒后隐藏提示
    } else {
      this.fetchApplyList();
    }
  },
});
