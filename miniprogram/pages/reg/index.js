const config = require('../../config/index');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    companyName: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    licenseNumber: '',
    email: '',
    verificationCode: '',
    isEmailValid: false,
    isFormValid: false,
    showPassword: false,
    showConfirmPassword: false,
    countdown: 0,
    passwordMismatch: false, // 新增
    emailInvalid: false, // 新增
  },

  // 输入框输入事件
  onInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData(
      {
        [field]: e.detail.value,
      },
      this.validateForm,
    );
  },

  // 切换密码显示状态
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword,
    });
  },

  // 切换确认密码显示状态
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword,
    });
  },

  // 表单验证
  validateForm() {
    const { companyName, phoneNumber, password, confirmPassword, licenseNumber, email, verificationCode } = this.data;
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isEmailValid = emailPattern.test(email);

    const passwordMismatch = password && confirmPassword && password !== confirmPassword;
    const emailInvalid = email && !isEmailValid;

    const isFormValid =
      companyName &&
      phoneNumber &&
      password &&
      confirmPassword &&
      licenseNumber &&
      email &&
      verificationCode &&
      isEmailValid &&
      !passwordMismatch;

    this.setData({
      isEmailValid,
      isFormValid,
      passwordMismatch, // 新增
      emailInvalid, // 新增
    });
  },

  getCode() {
    if (this.data.isEmailValid && this.data.countdown === 0) {
      wx.showLoading({
        title: '请求中...',
      });

      // 数据字段映射
      const params = {
        has_token: false,
        islogin: true,
        email: this.data.email,
        appVersion: '40',
        appType: 'ANDROID',
        imei: 'null,null,null',
        phoneSend: '1',
        islogin: 'true',
        phoneFlag: 'sdk gphone64 x86 64',
        isLogin: '1',
      };

      wx.request({
        url: `${config.defaultURL}/tsAdvComRegController.do?sendToEmailVerificationCode`, // 替换为你的服务器接口地址
        method: 'POST',
        data: { ...params },
        header: {
          'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
        },
        success: (res) => {
          wx.hideLoading();
          const { head } = res.data;
          if (head && head.code == 100) {
            wx.showToast({
              title: '验证码已发送',
              icon: 'success',
            });
            this.setData({
              countdown: 60,
            });
            this.startCountdown();
          } else {
            wx.showToast({
              title: head.message || '发送验证码失败',
              icon: 'none',
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '发送验证码失败，请重试',
            icon: 'none',
          });
          console.error('发送验证码请求失败:', err);
        },
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    if (this.data.countdown > 0) {
      setTimeout(() => {
        this.setData({
          countdown: this.data.countdown - 1,
        });
        this.startCountdown();
      }, 1000);
    }
  },

  // 注册提交函数
  register() {
    if (this.data.isFormValid) {
      wx.showLoading({
        title: '请求中...',
      });

      const params = {
        fullName: this.data.companyName,
        userName: this.data.phoneNumber,
        password: this.data.password,
        extChar2: this.data.licenseNumber,
        extChar9: this.data.verificationCode,
        email: this.data.email,
        saveAdvComAccRegInfo: true,
        appVersion: '40',
        appType: 'ANDROID',
        imei: 'null,null,null',
        phoneSend: '1',
        islogin: 'true',
        phoneFlag: 'sdk gphone64 x86 64',
        isLogin: '1',
      };

      wx.request({
        url: `${config.defaultURL}/tsAdvComRegController.do?saveAdvComAccRegInfo`, // 替换为你的服务器接口地址
        method: 'POST', // 修改为 POST 方法
        data: { ...params },
        header: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        success: (res) => {
          const response = res.data;
          if (response.head && response.head.code === 100) {
            wx.showToast({
              title: '注册成功',
              icon: 'success',
              duration: 2000, // 显示时间
              success: function () {
                setTimeout(() => {
                  wx.navigateBack();
                }, 2000); // 等待提示框显示完毕后再返回上一页
              },
            });
          } else {
            wx.showToast({
              title: response.head ? response.head.message : '注册失败',
              icon: 'none',
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '注册失败，请重试',
            icon: 'none',
          });
          console.error('注册请求失败:', err);
        },
      });
    } else if (this.data.passwordMismatch) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none',
      });
    } else if (this.data.emailInvalid) {
      wx.showToast({
        title: '邮箱格式错误',
        icon: 'none',
      });
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
