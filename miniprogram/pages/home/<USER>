const config = require('../../config/index');
Page({
  data: {
    userId: '',
    userNameLocation: '',
    boxStyle: '',
  },

  onLoad() {
    this.loadUserInfo();
    this.queryElements();
  },

  handleExit() {
    wx.redirectTo({
      url: '/pages/login/index',
    });
  },

  queryElements() {
    let rect = null;
    if (wx.getMenuButtonBoundingClientRect) {
      rect = wx.getMenuButtonBoundingClientRect();
    }
    if (!rect) return;
    wx.getSystemInfo({
      success: (res) => {
        // 系统导航
        let sysH = res.statusBarHeight;
        let titH = (rect.top - res.statusBarHeight) * 2 + rect.height;
        const boxStyleList = [];
        boxStyleList.push(`--lc-sysH: ${sysH}px`);
        boxStyleList.push(`--lc-titH:${titH + sysH}px`);
        this.setData({
          boxStyle: `${boxStyleList.join('; ')}`,
        });
      },
      fail: (err) => {
        console.error('navbar 获取系统信息失败', err);
      },
    });
  },

  loadUserInfo() {
    const userId = wx.getStorageSync('userData')?.businessObject?.frUserName || '未登录';
    const userNameLocation = wx.getStorageSync('userData')?.businessObject?.employName || '未登录';
    if (userId == '未登录') {
      wx.redirectTo({
        url: '/pages/login/index',
      });
    }
    this.setData({
      userId,
      userNameLocation,
    });
  },

  handleTap(e) {
    const { index } = e.currentTarget.dataset;
    // 根据index处理不同的点击事件
    if (index == 0) {
      // 跳转到制作前选址页面
      wx.navigateTo({
        url: '/pages/choosePlace/index',
      });
    } else if (index == 1) {
      // 跳转到施工后拍照页面
      wx.navigateTo({
        url: '/pages/act/index?bpmKey=BPM006&bpmStatus=3',
      });
    } else if (index == 2) {
      // 跳转到门头列表页面
      wx.navigateTo({
        url: '/pages/act/index?bpmStatus=3',
      });
    } else if (index == 3) {
      // 跳转到店铺登记页面
      wx.navigateTo({
        url: '/pages/shopPicker/index',
      });
    } else if (index == 4) {
      // 跳转到设置页面
      wx.navigateTo({
        url: '/pages/settings/index',
      });
    } else {
      console.log('index');
    }
  },
});
