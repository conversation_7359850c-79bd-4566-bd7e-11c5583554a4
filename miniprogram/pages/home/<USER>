/* app.scss */
.container {
  padding: 0rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  width: 702rpx;
  height: 140rpx;
  position: relative;
  display: flex;
  align-items: center;
  background-image: linear-gradient(to bottom, #fdf8ee 0%, #ffffff 50%);
  margin: 24rpx;
  margin-top: -80rpx;
  border-radius: 24rpx;
}

.grid-item_child {
  width: 340rpx;
  height: 208rpx;
  background: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/xuan.png') 340rpx 208rpx no-repeat;
  box-sizing: border-box;
}

@font-face {
  font-family: AlimamaShuHeiTi;
  src: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/AlimamaShuHeiTi.ttf');
}
.grid-card {
  display: flex;
  padding: 24rpx;
  box-sizing: border-box;
  margin-top: 24rpx;
  flex-wrap: wrap;
  .grid-item {
    box-sizing: border-box;
    width: 340rpx;
    height: 208rpx;
    margin-left: 22rpx;
    margin-bottom: 24rpx;
    padding: 24rpx;
    &:nth-child(2n-1) {
      margin-left: 0;
    }
    text {
      font-weight: bold;
      font-size: 32rpx;
      color: #222222;
      line-height: 38rpx;
      font-family: AlimamaShuHeiTi;
    }
    &:nth-child(1) {
      background-image: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/xuan.png');
      background-size: 340rpx 208rpx;
      background-repeat: no-repeat;
    }
    &:nth-child(2) {
      background-image: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/xuan2.png');
      background-size: 340rpx 208rpx;
      background-repeat: no-repeat;
    }
    &:nth-child(3) {
      background-image: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/xuan3.png');
      background-size: 340rpx 208rpx;
      background-repeat: no-repeat;
    }
    &:nth-child(4) {
      background-image: url('http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/xuan4.png');
      background-size: 340rpx 208rpx;
      background-repeat: no-repeat;
    }
  }
}

.user-info-section {
  display: flex;
  flex-direction: column;
}

.user-id {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.user-name-location {
  font-size: 24rpx;
  color: #666666;
}

.user-exit {
  width: 60rpx;
  height: 140rpx; /* 调整高度以适应上下布局 */
  margin-left: 250rpx;
  display: flex;
  flex-direction: column; /* 设置主轴方向为垂直 */
  align-items: center;
  box-sizing: border-box;
  margin-top: 50rpx;
  .exit-img {
    box-sizing: border-box;
    width: 50rpx;
    height: 50rpx; /* 调整高度以适应上下布局 */
  }
  .exit-text {
    text-align: center; /* 文本居中 */
    font-size: 24rpx;
    margin-top: 10rpx;
  }
}

.profile-pic {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin: 40rpx;
}
.profile-img {
  width: 80rpx;
  height: 80rpx;
}
$background-back: 'http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/<EMAIL>';
$title-back: 'http://gmarket.etianneng.cn:9528/image/14681-03-29/wxapp/<EMAIL>';
.back-pic {
  width: 750rpx;
  height: 480rpx;
  background-image: url($background-back);
  background-size: 750rpx 480rpx;
  background-repeat: no-repeat;
  box-sizing: border-box;
  .ggs-log {
    box-sizing: border-box;
    padding-top: var(--lc-sysH);
    height: var(--lc-titH);
    display: flex;
    align-items: center;
    view {
      width: 750rpx;
      height: 48rpx;
      background-image: url($title-back);
      background-size: 318rpx 48rpx;
      background-repeat: no-repeat;
      background-position: 24rpx 0;
    }
  }
}
