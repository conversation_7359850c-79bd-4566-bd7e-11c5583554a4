<view class="page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">📤</view>
    <view class="header-content">
      <view class="header-title">施工后效果上传</view>
      <view class="header-desc">上传施工完成后的照片和视频</view>
    </view>
  </view>

  <!-- 基本信息卡片 -->
  <view class="info-card">
    <view class="card-title">
      <view class="title-icon">📋</view>
      <text>基本信息</text>
    </view>

    <!-- 选址位置 -->
    <view class="info-item">
      <view class="info-label">选址位置</view>
      <view class="info-value">{{detail.gpsAddress}}</view>
    </view>

    <!-- 活动单号 -->
    <view class="info-item">
      <view class="info-label">活动单号</view>
      <view class="info-value">{{detail.actCode}}</view>
    </view>

    <!-- 广告公司 -->
    <view class="info-item">
      <view class="info-label">广告公司</view>
      <view class="info-value">{{detail.advName}}</view>
    </view>

    <!-- 终端名称 -->
    <view class="info-item">
      <view class="info-label">终端名称</view>
      <view class="info-value">{{detail.terminalName}}</view>
    </view>
  </view>

  <!-- 施工位置 -->
  <view class="location-card">
    <view class="card-title">
      <view class="title-icon">📍</view>
      <text>施工位置</text>
    </view>
    <view class="location-row">
      <view class="location-container">
        <!-- 定位动画 -->
        <view class="location-animation" wx:if="{{isLocating}}">
          <view class="location-pulse"></view>
          <view class="location-dot"></view>
        </view>
        <view class="location-value {{isLocating ? 'locating' : ''}}">
          {{isLocating ? '正在定位...' : (address || '点击获取位置')}}
        </view>
      </view>
      <view class="location-button" bindtap="reLocate">
        <image
          src="../../resource/img/locate.png"
          class="location-icon {{isLocating ? 'rotating' : ''}}"
        />
      </view>
    </view>
  </view>

  <!-- 施工详情 -->
  <view class="content-container">
    <block wx:for="{{detailVos}}" wx:key="id">
      <view class="detail-card">
        <view class="card-title">
          <view class="title-icon">🏗️</view>
          <text>施工详情 {{index + 1}}</text>
        </view>

        <!-- 制作类型 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">🏭</view>
            <text>制作类型</text>
          </view>
          <view class="section-content">{{item.adType == 2 ? '非门头' : '门头'}}</view>
        </view>

        <!-- 位置信息 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">📍</view>
            <text>位置信息</text>
            <text class="section-note">(非现场选址可忽略)</text>
          </view>
          <view class="section-content">{{item.place || '暂无位置信息'}}</view>
        </view>

        <!-- 施工前照片 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">📷</view>
            <text>施工前照片</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '105'}}" class="media-item">
                <t-image
                  src="{{pic.imgPath}}"
                  mode="aspectFill"
                  class="media-image"
                  bindtap="reviewImg"
                  data-url="{{pic.imgPath}}"
                />
              </view>
            </block>
          </view>
        </view>

        <!-- 施工前小视频 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">🎬</view>
            <text>施工前小视频</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '145'}}" class="media-item">
                <video src="{{pic.imgPath}}" controls class="media-video"></video>
              </view>
            </block>
          </view>
        </view>

        <!-- 备注 -->
        <view class="detail-section" wx:if="{{item.remarks}}">
          <view class="section-title">
            <view class="section-icon">📝</view>
            <text>备注信息</text>
          </view>
          <view class="remarks-content">
            <t-textarea value="{{item.remarks}}" placeholder="暂无备注" class="remarks-textarea" disabled="true" />
          </view>
        </view>

        <!-- 施工后照片（近景+远景）上传 -->
        <view class="detail-section upload-section">
          <view class="section-title">
            <view class="section-icon">📸</view>
            <text>施工后照片（近景+远景）</text>
            <text class="required-mark">*</text>
          </view>
          <view class="upload-container">
            <t-upload
              mediaType="{{['image']}}"
              files="{{item.uploadedImages}}"
              data-id="{{item.id}}"
              source="media"
              config="{{phtotConfig}}"
              bind:add="handleAdd"
              bind:remove="handleRemove"
            ></t-upload>
          </view>
        </view>

        <!-- 施工后效果小视频上传 -->
        <view class="detail-section upload-section">
          <view class="section-title">
            <view class="section-icon">🎥</view>
            <text>施工后效果小视频（360°环绕）</text>
            <text class="required-mark">*</text>
          </view>
          <view class="upload-container">
            <t-upload
              mediaType="{{['video']}}"
              max="1"
              data-id="{{item.id}}"
              files="{{item.uploadedVideos}}"
              source="media"
              config="{{videoConfig}}"
              bind:add="handleVideoAdd"
              bind:remove="handleVideoRemove"
            ></t-upload>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 审核驳回原因 -->
  <view wx:if="{{detail.rejectReason}}" class="reject-card">
    <view class="card-title alert">
      <view class="title-icon">⚠️</view>
      <text>审核驳回原因</text>
    </view>
    <view class="reject-content">
      <t-textarea
        value="{{detail.rejectReason}}"
        bindinput="onInputRejectionReason"
        class="reject-textarea"
        disabled="true"
      />
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <t-button theme="primary" size="large" block bindtap="onSubmit" class="submit-button">
      <view class="button-content">
        <text class="button-icon">📤</text>
        <text>提交施工效果</text>
      </view>
    </t-button>
  </view>
</view>
