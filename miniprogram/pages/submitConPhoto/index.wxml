<view class="page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">📤</view>
    <view class="header-content">
      <view class="header-title">施工后效果上传</view>
      <view class="header-desc">上传施工完成后的照片和视频</view>
    </view>
  </view>

  <!-- 基本信息卡片 -->
  <view class="info-card">
    <view class="card-title">
      <view class="title-icon">📋</view>
      <text>基本信息</text>
    </view>

    <!-- 选址位置 -->
    <view class="info-item">
      <view class="info-label">选址位置</view>
      <view class="info-value">{{detail.gpsAddress}}</view>
    </view>

    <!-- 活动单号 -->
    <view class="info-item">
      <view class="info-label">活动单号</view>
      <view class="info-value">{{detail.actCode}}</view>
    </view>

    <!-- 广告公司 -->
    <view class="info-item">
      <view class="info-label">广告公司</view>
      <view class="info-value">{{detail.advName}}</view>
    </view>

    <!-- 终端名称 -->
    <view class="info-item">
      <view class="info-label">终端名称</view>
      <view class="info-value">{{detail.terminalName}}</view>
    </view>
  </view>

  <!-- 施工位置 -->
  <view class="location-card">
    <view class="card-title">
      <view class="title-icon">📍</view>
      <text>施工位置</text>
    </view>
    <view class="location-row">
      <view class="location-container">
        <!-- 定位动画 -->
        <view class="location-animation" wx:if="{{isLocating}}">
          <view class="location-pulse"></view>
          <view class="location-dot"></view>
        </view>
        <view class="location-value {{isLocating ? 'locating' : ''}}">
          {{isLocating ? '正在定位...' : (address || '点击获取位置')}}
        </view>
      </view>
      <view class="location-button" bindtap="reLocate">
        <image
          src="../../resource/img/locate.png"
          class="location-icon {{isLocating ? 'rotating' : ''}}"
        />
      </view>
    </view>
  </view>

  <!-- 其他内容 -->
  <view class="section all-content-block">
    <block wx:for="{{detailVos}}" wx:key="id">
      <!-- 制作类型 -->
      <view class="section">
        <view class="label">制作类型</view>
        <view class="content">{{item.adType == 2 ? '非门头' : '门头'}}</view>
      </view>
      <!-- 位置信息 -->
      <view class="section">
        <view class="label">位置信息(非现场选址可忽略)</view>
        <view class="content">{{item.place}}</view>
      </view>

      <!-- 照片 -->
      <view class="section">
        <view class="label">照片</view>
        <view class="images">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '105'}}" class="image-item">
              <t-image
                src="{{item.imgPath}}"
                mode="aspectFit"
                class="image"
                bindtap="reviewImg"
                data-url="{{item.imgPath}}"
              />
            </view>
          </block>
        </view>
      </view>

      <!-- 小视频 -->
      <view class="section">
        <view class="label">小视频</view>
        <view class="videos">
          <block wx:for="{{item.picVoList}}" wx:key="id">
            <view wx:if="{{item.imgType == '145'}}" class="video-item">
              <video src="{{item.imgPath}}" controls class="video"></video>
            </view>
          </block>
        </view>
      </view>

      <!-- 备注 -->
      <view class="section">
        <view class="label">备注</view>
        <view class="content">
          <t-textarea value="{{item.remarks}}" placeholder="请输入备注" class="textarea" disabled="true" />
        </view>
      </view>

      <!-- 施工后照片（近景+远景）上传 -->
      <view class="section">
        <view class="labelGroup">
          <label class="redLab">*</label>
          <view class="label">施工后照片（近景+远景）</view>
        </view>
        <t-upload
          mediaType="{{['image']}}"
          files="{{item.uploadedImages}}"
          data-id="{{item.id}}"
          source="media"
          config="{{phtotConfig}}"
          bind:add="handleAdd"
          bind:remove="handleRemove"
        ></t-upload>
      </view>

      <!-- 施工后效果小视频上传 -->
      <view class="section">
        <view class="labelGroup">
          <label class="redLab">*</label>
          <view class="label">施工后效果小视频（对着门头360°转一圈）</view>
        </view>
        <t-upload
          mediaType="{{['video']}}"
          max="1"
          data-id="{{item.id}}"
          files="{{item.uploadedVideos}}"
          source="media"
          config="{{videoConfig}}"
          bind:add="handleVideoAdd"
          bind:remove="handleVideoRemove"
        ></t-upload>
      </view>
    </block>
  </view>

  <!-- 审核驳回原因 -->
  <view wx:if="{{detail.rejectReason}}" class="section">
    <view class="label rejection-reason">审核驳回原因</view>
    <view class="content">
      <t-textarea value="{{detail.rejectReason}}" bindinput="onInputRejectionReason" class="textarea" disabled="true" />
    </view>
  </view>
  <!-- 提交按钮 -->
  <view class="section">
    <t-button theme="primary" block bindtap="onSubmit">提交</t-button>
  </view>
</view>
