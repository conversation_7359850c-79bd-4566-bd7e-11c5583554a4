/* 页面容器 */
.page {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
}

.header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.header-desc {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 定位动画样式 */
.location-container {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
}

.location-animation {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #1890ff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 定位中的文字样式 */
.location-value.locating {
  color: #1890ff;
  font-style: italic;
}

/* 定位按钮旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #1890ff, #722ed1);
  }
}

/* 位置卡片 */
.location-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #52c41a, #1890ff);
  }
}

/* 卡片标题 */
.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;

  &.alert {
    color: #ff4d4f;
    border-bottom-color: rgba(255, 77, 79, 0.2);
  }
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

/* 信息项 */
.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &.alert {
    background-color: #fff2f0;
    border: 2rpx solid #ffccc7;
  }
}

.info-label {
  min-width: 160rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-word;
}

/* 位置行 */
.location-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.location-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.location-button {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: #e0e0e0;
  }
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 内容容器 */
.content-container {
  padding: 0 20rpx 20rpx;
}

/* 详情卡片 */
.detail-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #52c41a, #1890ff);
  }
}

/* 详情区域 */
.detail-section {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-note {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 8rpx;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 8rpx;
  font-weight: bold;
}

/* 区域内容 */
.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  word-break: break-word;
}

/* 媒体网格 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

/* 媒体项 */
.media-item {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f0f0f0;
  aspect-ratio: 1 / 1;
}

/* 媒体图片 */
.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 媒体视频 */
.media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 备注内容 */
.remarks-content {
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

/* 备注文本区域 */
.remarks-textarea {
  width: 100%;
  min-height: 160rpx;
  border: none;
  background-color: transparent;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 上传区域 */
.upload-section {
  margin-top: 40rpx;
}

.upload-container {
  margin-top: 16rpx;
}

/* 驳回卡片 */
.reject-card {
  background-color: #fff2f0;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 77, 79, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #ff4d4f, #ff7a45);
  }
}

.reject-content {
  padding: 16rpx;
  background-color: rgba(255, 77, 79, 0.05);
  border-radius: 12rpx;
}

.reject-textarea {
  width: 100%;
  min-height: 160rpx;
  border: none;
  background-color: transparent;
  font-size: 28rpx;
  color: #ff4d4f;
  line-height: 1.6;
}

/* 提交按钮 */
.submit-container {
  padding: 32rpx 20rpx;
}

.submit-button {
  border-radius: 12rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.2) !important;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.button-icon {
  font-size: 32rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card,
.detail-card,
.location-card,
.reject-card {
  animation: fadeIn 0.3s ease-out;
}
