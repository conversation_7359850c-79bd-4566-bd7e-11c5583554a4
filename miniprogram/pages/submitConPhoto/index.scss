/* 定位动画样式 */
.location-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-animation {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 定位中的文字样式 */
.content.locating {
  color: #1890ff;
  font-style: italic;
}

/* 定位按钮旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 基本样式 */
.container {
  padding: 10px;
}

/* 各个区域的间距 */
.section {
  margin-bottom: 10px; /* 调整区域之间的间距 */
  padding: 5px; /* 内部间距 */
  border-radius: 8px; /* 圆角 */
}

/* 单列布局 */
.single-block {
  background-color: #f1f3f5;
}

/* 分组布局 */
.grouped-block {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 每组项之间的间距 */
  background-color: #f1f3f5;
}

/* 分组项样式 */
.group-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px;
  font-size: 15px;
}

/* 标签样式 */
.label {
  margin-right: 10px;
  flex: 1; /* 让标签占据最小宽度 */
  color: rgb(95, 94, 94);
  font-size: 15px;
  padding: 7px;
}

/* 内容样式 */
.content {
  flex: 2; /* 内容部分占据更多宽度 */
  color: rgb(3, 3, 3);
  font-size: 15px;
  padding: 7px;
}

/* 其他内容区域 */
.all-content-block {
  background-color: #ffffff;
}

/* 图片和视频布局 */
.images,
.videos {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 图片和视频之间的间距 */
}

/* 图片和视频项 */
.image-item,
.video-item {
  flex: 1 1 30%; /* 自动调整宽度 */
  max-width: 30%; /* 最大宽度30% */
}

/* 图片样式 */
.image {
  width: 100%;
  height: 72px; /* 设置图片固定高度 */
  object-fit: cover;
}

/* 视频样式 */
.video {
  width: 100%;
  max-width: 100%;
  max-height: 80px; /* 限制视频最大高度 */
  object-fit: cover;
}

/* 备注文本区域 */
.textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;
  background-color: rgba($color: #9e7272, $alpha: 1);
}

.location-row {
  display: flex;
  align-items: center;
}

.rejection-reason {
  color: red;
  font-weight: bold;
}

.labelGroup {
  display: flex;
  align-items: baseline; /* 下标对齐 */
}
.redLab {
  color: rgb(247, 9, 9);
}
