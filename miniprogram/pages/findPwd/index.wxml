<view class="container">
  <view class="logo">
    <image src="../../resource/img/icon.png" mode="aspectFit"></image>
  </view>
  <view class="title">天能数字化精准营销平台</view>
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>用户名</label>
    </view>
    <t-input placeholder="请输入用户名" value="{{username}}" bindchange="onInput" data-field="username" />
  </view>
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>邮箱</label>
    </view>
    <t-input placeholder="请输入邮箱" value="{{email}}" bindchange="onInput" data-field="email" />
    <view class="error-message" wx:if="{{emailInvalid}}">邮箱格式错误</view>
  </view>
  <view class="form-item verify-code-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>邮箱验证码</label>
    </view>
    <t-input
      placeholder="请输入邮箱验证码"
      class="verify-input"
      value="{{verificationCode}}"
      bindchange="onInput"
      data-field="verificationCode"
    />
    <t-button bindtap="getCode" class="get-code-btn" size="small" disabled="{{countdown > 0 || !isEmailValid}}">
      {{countdown > 0 ? countdown + 's' : '获取验证码'}}
    </t-button>
  </view>
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>新密码</label>
    </view>
    <view class="password-input">
      <t-input
        placeholder="请输入新密码"
        type="{{showPassword ? 'text' : 'password'}}"
        value="{{newPassword}}"
        bindchange="onInput"
        data-field="newPassword"
      />
      <button bindtap="togglePasswordVisibility">{{showPassword ? '🙈' : '👁️'}}</button>
    </view>
  </view>
  <view class="form-item">
    <view class="labelGroup">
      <label class="redLab">*</label>
      <label>再次输入新密码</label>
    </view>
    <view class="password-input">
      <t-input
        placeholder="请再次输入新密码"
        type="{{showConfirmPassword ? 'text' : 'password'}}"
        value="{{confirmPassword}}"
        bindchange="onInput"
        data-field="confirmPassword"
      />
      <button bindtap="toggleConfirmPasswordVisibility">{{showConfirmPassword ? '🙈' : '👁️'}}</button>
    </view>
    <view class="error-message" wx:if="{{passwordMismatch}}">两次密码输入不一致</view>
  </view>
  <t-button block theme="primary" bindtap="resetPassword" class="reset-btn" disabled="{{!isFormValid}}">确定</t-button>
</view>
