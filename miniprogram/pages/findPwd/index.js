const config = require('../../config/index');
Page({
  data: {
    username: '',
    email: '',
    verificationCode: '',
    newPassword: '',
    confirmPassword: '',
    isEmailValid: false,
    isFormValid: false,
    showPassword: false,
    showConfirmPassword: false,
    countdown: 0,
    passwordMismatch: false, // 新增
    emailInvalid: false, // 新增
  },

  // 输入框输入事件
  onInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData(
      {
        [field]: e.detail.value,
      },
      this.validateForm,
    );
  },

  // 切换密码显示状态
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword,
    });
  },

  // 切换确认密码显示状态
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword,
    });
  },

  // 表单验证
  validateForm() {
    const { username, email, verificationCode, newPassword, confirmPassword } = this.data;
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isEmailValid = emailPattern.test(email);

    const passwordMismatch = newPassword && confirmPassword && newPassword !== confirmPassword;
    const emailInvalid = email && !isEmailValid;

    const isFormValid =
      username && email && verificationCode && newPassword && confirmPassword && isEmailValid && !passwordMismatch;

    this.setData({
      isEmailValid,
      isFormValid,
      passwordMismatch, // 新增
      emailInvalid, // 新增
    });
  },

  // 获取验证码函数
  getCode() {
    if (this.data.isEmailValid && this.data.countdown === 0) {
      wx.showLoading({
        title: '请求中...',
      });

      // 数据字段映射
      const params = {
        has_token: false,
        islogin: true,
        email: this.data.email,
        userName: this.data.username,
        appVersion: '40',
        appType: 'ANDROID',
        imei: 'null,null,null',
        phoneSend: '1',
        islogin: 'true', // 这个字段重复，可能需要去掉其中一个
        phoneFlag: 'sdk gphone64 x86 64',
        isLogin: '1', // 这个字段重复，可能需要去掉其中一个
      };

      wx.request({
        url: `${config.defaultURL}/tsAdvComRegController.do?sendToEmailVerificationCodeEdit`, // 替换为你的服务器接口地址
        method: 'POST',
        data: { ...params },
        header: {
          'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
        },
        success: (res) => {
          wx.hideLoading();
          const { head } = res.data;
          if (head && head.code == 100) {
            // 根据新的成功码值
            wx.showToast({
              title: '验证码已发送',
              icon: 'success',
            });
            this.setData({
              countdown: 60,
            });
            this.startCountdown();
          } else {
            wx.showToast({
              title: head.message || '发送验证码失败',
              icon: 'none',
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '发送验证码失败，请重试',
            icon: 'none',
          });
          console.error('发送验证码请求失败:', err);
        },
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    if (this.data.countdown > 0) {
      setTimeout(() => {
        this.setData({
          countdown: this.data.countdown - 1,
        });
        this.startCountdown();
      }, 1000);
    }
  },

  // 重置密码提交函数
  resetPassword() {
    if (this.data.isFormValid) {
      wx.showLoading({
        title: '请求中...',
      });

      // 数据字段映射
      const params = {
        userName: this.data.username, // 用户名或手机号
        password: this.data.newPassword, // 旧密码
        email: this.data.email,
        extChar9: this.data.verificationCode,
        has_token: false,
        islogin: true,
        email: this.data.email,
        appVersion: '40',
        appType: 'ANDROID',
        imei: 'null,null,null',
        phoneSend: '1',
        islogin: 'true',
        phoneFlag: 'sdk gphone64 x86 64',
        isLogin: '1',
      };

      wx.request({
        url: `${config.defaultURL}/tsAdvComRegController.do?editAdvComAccRegInfo`,
        method: 'POST',
        data: { ...params },
        header: {
          'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
        },
        success: (res) => {
          wx.hideLoading();
          const { head } = res.data;
          if (head && head.code == 100) {
            wx.showToast({
              title: '密码重置成功',
              icon: 'success',
            });
            // 密码重置成功后的逻辑，例如跳转到登录页面
            wx.navigateTo({
              url: '/pages/login/index',
            });
          } else {
            wx.showToast({
              title: head.message || '密码重置失败',
              icon: 'none',
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '密码重置失败，请重试',
            icon: 'none',
          });
          console.error('密码重置请求失败:', err);
        },
      });
    } else if (this.data.passwordMismatch) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none',
      });
    } else if (this.data.emailInvalid) {
      wx.showToast({
        title: '邮箱格式错误',
        icon: 'none',
      });
    }
  },
});
