const config = require('../../config/index');
Page({
  data: {
    detail: {},
    detailVos: [],
  },
  onLoad(options) {
    const itemStr = decodeURIComponent(options.act);
    const data = JSON.parse(itemStr);

    // 遍历 detailVos 数组，替换 picVoList 中的 imgPath 域名
    const updatedDetailVos = data.detailVos.map((detailVo) => {
      if (detailVo.picVoList && detailVo.picVoList.length > 0) {
        detailVo.picVoList = detailVo.picVoList.map((picVo) => {
          // 替换 imgPath 中的域名
          if (picVo.imgPath) {
            picVo.imgPath = picVo.imgPath.replace(/^https?:\/\/[^\/:]+(:\d+)?/, `${config.imgURL}`);
          }
          return picVo;
        });
      }
      return detailVo;
    });

    // 将处理后的数据设置到页面的 data 中
    this.setData({
      detail: data,
      detailVos: updatedDetailVos,
    });
  },

  handleClose() {
    console.log('Image viewer closed');
  },

  reviewImg(e) {
    wx.previewImage({
      urls: [e.currentTarget.dataset.url],
    });
  },
});
