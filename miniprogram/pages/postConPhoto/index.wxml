<view class="page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">📋</view>
    <view class="header-content">
      <view class="header-title">门头施工后效果</view>
      <view class="header-desc">查看施工完成后的效果照片和视频</view>
    </view>
  </view>

  <!-- 基本信息卡片 -->
  <view class="info-card">
    <view class="card-title">
      <view class="title-icon">📍</view>
      <text>基本信息</text>
    </view>

    <!-- 选址位置 -->
    <view class="info-item">
      <view class="info-label">选址位置</view>
      <view class="info-value">{{detail.gpsAddress}}</view>
    </view>

    <!-- 活动单号 -->
    <view class="info-item">
      <view class="info-label">活动单号</view>
      <view class="info-value">{{detail.actCode}}</view>
    </view>

    <!-- 广告公司 -->
    <view class="info-item">
      <view class="info-label">广告公司</view>
      <view class="info-value">{{detail.advName}}</view>
    </view>

    <!-- 终端名称 -->
    <view class="info-item">
      <view class="info-label">终端名称</view>
      <view class="info-value">{{detail.terminalName}}</view>
    </view>

    <!-- 施工位置 -->
    <view class="info-item">
      <view class="info-label">施工位置</view>
      <view class="info-value">{{detail.constructionAddress || '暂无'}}</view>
    </view>
  </view>

  <!-- 施工详情 -->
  <view class="content-container">
    <block wx:for="{{detailVos}}" wx:key="id">
      <view class="detail-card">
        <view class="card-title">
          <view class="title-icon">🏗️</view>
          <text>施工详情 {{index + 1}}</text>
        </view>

        <!-- 位置信息 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">📍</view>
            <text>位置信息</text>
          </view>
          <view class="section-content">{{item.place}}</view>
        </view>

        <!-- 施工前照片 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">📷</view>
            <text>施工前照片</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '105'}}" class="media-item">
                <t-image
                  src="{{pic.imgPath}}"
                  mode="aspectFill"
                  class="media-image"
                  bindtap="reviewImg"
                  data-url="{{pic.imgPath}}"
                />
              </view>
            </block>
          </view>
        </view>

        <!-- 施工前小视频 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">🎬</view>
            <text>施工前小视频</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '145'}}" class="media-item">
                <video src="{{pic.imgPath}}" controls class="media-video"></video>
              </view>
            </block>
          </view>
        </view>

        <!-- 备注 -->
        <view class="detail-section" wx:if="{{item.remarks}}">
          <view class="section-title">
            <view class="section-icon">📝</view>
            <text>备注信息</text>
          </view>
          <view class="remarks-content">
            <t-textarea value="{{item.remarks}}" placeholder="暂无备注" class="remarks-textarea" disabled="true" />
          </view>
        </view>

        <!-- 施工后照片（近景+远景） -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">📸</view>
            <text>施工后照片（近景+远景）</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '155'}}" class="media-item">
                <t-image
                  src="{{pic.imgPath}}"
                  mode="aspectFill"
                  class="media-image"
                  bindtap="reviewImg"
                  data-url="{{pic.imgPath}}"
                />
              </view>
            </block>
          </view>
        </view>

        <!-- 施工后效果小视频 -->
        <view class="detail-section">
          <view class="section-title">
            <view class="section-icon">🎥</view>
            <text>施工后效果小视频（360°环绕）</text>
          </view>
          <view class="media-grid">
            <block wx:for="{{item.picVoList}}" wx:key="id" wx:for-item="pic">
              <view wx:if="{{pic.imgType == '161'}}" class="media-item">
                <video src="{{pic.imgPath}}" controls class="media-video"></video>
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
  </view>
</view>
