/* 页面容器 */
.page {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
}

.header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.header-desc {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #1890ff, #722ed1);
  }
}

/* 卡片标题 */
.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

/* 信息项 */
.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  min-width: 160rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-word;
}

/* 内容容器 */
.content-container {
  padding: 0 20rpx 20rpx;
}

/* 详情卡片 */
.detail-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #52c41a, #1890ff);
  }
}

/* 详情区域 */
.detail-section {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

/* 区域内容 */
.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  word-break: break-word;
}

/* 媒体网格 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

/* 媒体项 */
.media-item {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f0f0f0;
  aspect-ratio: 1 / 1;
}

/* 媒体图片 */
.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 媒体视频 */
.media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 备注内容 */
.remarks-content {
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

/* 备注文本区域 */
.remarks-textarea {
  width: 100%;
  min-height: 160rpx;
  border: none;
  background-color: transparent;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card,
.detail-card {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .page-header {
    margin: 16rpx;
    padding: 24rpx;
  }

  .header-title {
    font-size: 28rpx;
  }

  .header-desc {
    font-size: 26rpx;
  }

  .info-card,
  .detail-card {
    padding: 24rpx;
    margin: 16rpx;
  }

  .info-label {
    min-width: 140rpx;
    font-size: 26rpx;
  }

  .info-value {
    font-size: 26rpx;
  }

  .media-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
