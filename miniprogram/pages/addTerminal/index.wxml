<view class="container">
  <!-- 店铺信息区域 -->
  <view class="section">
    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input placeholder="请输入店铺名称" label="店铺名称" bind:change="onInputChange" data-field="storeName" />
      </view>
    </view>

    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input placeholder="请输入店主姓名" label="店主姓名" bind:change="onInputChange" data-field="ownerName" />
      </view>
    </view>

    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input placeholder="请输入店主手机" label="店主手机" bind:change="onInputChange" data-field="ownerPhone" />
      </view>
    </view>
  </view>

  <!-- 地址信息区域 -->
  <view class="section">
    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input label="终端区域" value="{{region}}" disabled style="flex: 1" />
      </view>
    </view>

    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input label="详细地址" value="{{address}}" disabled bind:change="onInputChange" data-field="address" />
      </view>
    </view>
  </view>

  <!-- 其他信息区域 -->
  <view class="section">
    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-cell
          class="mb-16"
          title="店铺性质"
          arrow
          hover
          note="{{selectedTerminalType.label || '请选择店铺性质'}}"
          bindtap="onTerminalTypePicker"
        />
        <t-picker
          visible="{{terminalTypeVisible}}"
          value="{{selectedTerminalTypeIndex}}"
          title="选择店铺性质"
          cancelBtn="取消"
          confirmBtn="确认"
          bindchange="onTerminalTypeChange"
          bindcancel="onTerminalTypeCancel"
        >
          <t-picker-item options="{{terminalTypes}}" />
        </t-picker>
      </view>
    </view>

    <view class="form-group">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-input
          placeholder="填写大概面积即可"
          label="门店面积 (㎡)"
          type="number"
          bind:change="onInputChange"
          data-field="storeArea"
        />
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-button">
    <t-button block theme="primary" bind:tap="onSubmit">提交</t-button>
  </view>
</view>
