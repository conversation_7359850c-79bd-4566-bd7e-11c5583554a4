const config = require('../../config/index');
import { POST } from '../../library/optimizer/request';
Page({
  data: {
    storeName: '',
    ownerName: '',
    ownerPhone: '',
    region: '',
    address: '',
    latitude: null,
    longitude: null,
    province: '',
    city: '',
    district: '',
    terminalTypes: [], // 店铺性质选项
    selectedTerminalType: '{}', // 选中的店铺性质对象
    terminalTypeVisible: false,
    storeArea: '', // 门店面积
  },

  onLoad() {
    this.getLocation();
    this.getTerminalTypes(); // 页面加载时获取店铺性质
  },

  reLocate: function () {
    this.getLocation();
  },

  // 处理输入变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [field]: e.detail.value,
    });
  },

  // 获取用户当前位置
  getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res;
        this.reverseGeocode(latitude, longitude);
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  // 地理位置反编码获取地址
  reverseGeocode(latitude, longitude) {
    const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${config.baiduMapKey}&output=json&coordtype=wgs84ll&location=${latitude},${longitude}`;
    wx.request({
      url,
      success: (res) => {
        if (res.data && res.data.result && res.data.result.addressComponent) {
          const addressComponent = res.data.result.addressComponent;
          const formattedAddress = res.data.result.formatted_address;
          this.setData({
            latitude: `${latitude}`,
            longitude: `${longitude}`,
            region: `${addressComponent.province} ${addressComponent.city} ${addressComponent.district}`,
            address: formattedAddress,
            province: addressComponent.province,
            city: addressComponent.city,
            district: addressComponent.district,
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取地址失败',
          icon: 'none',
        });
      },
    });
  },

  // 获取店铺性质选项
  getTerminalTypes() {
    const params = {};
    POST(`/tmTerminalForSfaController.do?getTerminalType`, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        const terminalTypes = res.map((item) => ({
          label: item.value,
          value: item.key,
        }));
        this.setData({
          terminalTypes,
        });
      })
      .catch((err) => {
        wx.showToast({
          title: '获取店铺性质失败',
          icon: 'none',
        });
      });
  },

  // 显示店铺性质选择器
  onTerminalTypePicker() {
    this.setData({ terminalTypeVisible: true });
  },

  // 取消选择店铺性质
  onTerminalTypeCancel() {
    this.setData({ terminalTypeVisible: false });
  },

  // 店铺性质选择变化
  onTerminalTypeChange(e) {
    const { value } = e.detail;
    let selectedTerminalType = this.data.terminalTypes.find((item) => item.value == value[0]) || {};
    this.setData({
      selectedTerminalType,
      terminalTypeVisible: false,
    });
  },

  // 提交数据
  onSubmit() {
    if (
      !this.data.storeName ||
      !this.data.ownerName ||
      !this.data.ownerPhone ||
      !this.data.address ||
      !this.data.storeArea ||
      !this.data.selectedTerminalType
    ) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    const params = {
      businessId: wx.getStorageSync('userData')?.businessObject?.frUserName + `${Date.now()}`,
      terminalName: this.data.storeName,
      linkman: this.data.ownerName,
      linkmanPhone: this.data.ownerPhone,
      province: this.data.province,
      city: this.data.city,
      area: this.data.district,
      address: this.data.address,
      extChar6: this.data.latitude,
      extChar5: this.data.longitude,
      terminalType: this.data.selectedTerminalType.value,
      extChar1: this.data.storeArea,
    };

    POST(`/tmTerminalForSfaController.do?saveTmTerminal`, params, {
      custom: {
        isLoading: true,
        msg: '保存中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000, // 显示时间
          success: function () {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000); // 等待提示框显示完毕后再返回上一页
          },
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.head ? err.head.message : '提交失败',
          icon: 'none',
        });
      });
  },
});
