const config = require('../../config/index');
Page({
  data: {
    userName: '',
    password: '',
    remember: true,
    showConfirmPassword: false,
  },

  // 切换确认密码显示状态
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword,
    });
  },

  navigateToReg() {
    wx.navigateTo({
      url: '/pages/reg/index',
    });
  },

  gotoForgotPassword() {
    wx.navigateTo({
      url: '/pages/findPwd/index',
    });
  },

  onUsernameInput(e) {
    this.setData({
      userName: e.detail.value,
    });
  },

  onPasswordInput(e) {
    this.setData({
      password: e.detail.value,
    });
  },

  onRememberChange(e) {
    this.setData({
      remember: e.detail.checked,
    });
  },

  onLogin() {
    const { userName, password, remember } = this.data;

    // 用户名正则校验，规则为7位数字加A01或A02或A03
    const userNamePattern = /^[0-9]{7}A0[1-3]$/;
    if (!userNamePattern.test(userName)) {
      wx.showToast({
        title: '请广告公司使用经销商子账号登录',
        icon: 'none',
      });
      return;
    }

    if (!userName || !password) {
      wx.showToast({
        title: '请填写用户名和密码',
        icon: 'none',
      });
      return;
    }

    const additionalParams = {
      appVersion: '40',
      appType: 'ANDROID',
      imei: 'null,null,null',
      phoneSend: '1',
      islogin: 'true',
      phoneFlag: 'sdk gphone64 x86 64',
      isLogin: '1',
    };

    wx.request({
      url: `${config.defaultURL}/loginPIController.do?checkUser`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      data: { userName, password, ...additionalParams },
      success: (res) => {
        if (res.data.head.code === 100) {
          wx.showToast({
            title: '登录成功',
            icon: 'success',
          });
          if (remember) {
            wx.setStorageSync('userName', userName);
            wx.setStorageSync('password', password);
          }
          // 将返回的数据存储到微信缓存中
          wx.setStorageSync('userData', res.data);
          wx.setStorageSync('token', res.data.head.token);
          wx.redirectTo({
            url: '/pages/home/<USER>',
          });
        } else {
          wx.showToast({
            title: '用户名或密码错误',
            icon: 'none',
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '登录失败，请稍后重试',
          icon: 'none',
        });
      },
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userName: wx.getStorageSync('userName'),
      password: wx.getStorageSync('password'),
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
