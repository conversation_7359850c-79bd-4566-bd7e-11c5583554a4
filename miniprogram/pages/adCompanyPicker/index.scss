/* 页面容器 */
.page {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 提示信息区域 */
.tip-container {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
}

.tip-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.tip-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 搜索区域 */
.search-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 2rpx solid #f0f0f0;
}

.search-wrapper {
  flex: 1;

  /* 优化搜索框内的action按钮样式 */
  :deep(.t-search__action) {
    color: #1890ff !important;
    font-weight: 600 !important;
  }
}

/* 内容区域 */
.content-container {
  padding: 20rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  line-height: 1.5;
  max-width: 500rpx;
  margin: 0 auto 20rpx;
}

.search-keyword {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  margin-top: 20rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 滚动视图 */
.scroll-view {
  max-height: calc(100vh - 300rpx);
  min-height: 400rpx;
}

/* 公司卡片 */
.company-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #1890ff, #722ed1);
  }

  &.selectable {
    cursor: pointer;

    &:hover {
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
      border-color: #1890ff;
    }

    &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
    }
  }
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.company-name {
  display: flex;
  align-items: center;
  flex: 1;
}

.company-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.name-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.company-id {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-family: 'Courier New', monospace;
}

/* 卡片内容 */
.card-content {
  space-y: 20rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.info-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-word;
}

/* 选择指示器 */
.select-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.select-text {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
}

.select-arrow {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: bold;
}

/* 没有更多数据 */
.no-more-data {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 100rpx;
    height: 2rpx;
    background-color: #e8e8e8;
  }

  &::before {
    left: -120rpx;
  }

  &::after {
    right: -120rpx;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.company-card {
  animation: fadeIn 0.3s ease-out;
}
