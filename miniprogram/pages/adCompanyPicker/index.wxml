<view class="page">
  <!-- 提示信息区域 -->
  <view class="tip-container">
    <view class="tip-icon">🏢</view>
    <view class="tip-content">
      <view class="tip-title">广告公司选择</view>
      <view class="tip-text">
        请从下方列表中选择合作的广告公司，支持按公司名称搜索查找。
      </view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <t-sticky>
    <view class="search-container">
      <view class="search-wrapper">
        <t-search
          placeholder="请输入广告公司名称"
          value="{{searchQuery}}"
          bind:change="onSearchInput"
          bind:submit="onSearch"
          bind:clear="onClear"
          shape="round"
          action="搜索"
          bind:action-click="onSearch"
        />
      </view>
    </view>
  </t-sticky>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 空状态 -->
    <view wx:if="{{adCompanies.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon">🔍</view>
      <view class="empty-title">暂无广告公司数据</view>
      <view class="empty-desc">请尝试其他搜索关键词或联系管理员添加广告公司</view>
      <view wx:if="{{searchQuery}}" class="search-keyword">当前搜索: "{{searchQuery}}"</view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading && adCompanies.length === 0}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在搜索广告公司...</view>
    </view>

    <!-- 广告公司列表 -->
    <scroll-view wx:if="{{adCompanies.length > 0}}" scroll-y class="scroll-view">
      <block wx:for="{{adCompanies}}" wx:key="id">
        <view class="company-card selectable" bindtap="selectAdCompany" data-company="{{item}}">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="company-name">
              <text class="company-icon">🏢</text>
              <text class="name-text">{{item.fullName}}</text>
            </view>
            <view class="company-id">ID: {{item.id}}</view>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="info-row">
              <view class="info-icon">👤</view>
              <view class="info-text">联系人：{{item.userName}}</view>
            </view>
          </view>

          <!-- 选择指示器 -->
          <view class="select-indicator">
            <text class="select-text">点击选择</text>
            <text class="select-arrow">→</text>
          </view>
        </view>
      </block>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{showNoMoreData}}" class="no-more-data">
        <text class="no-more-text">已显示全部广告公司</text>
      </view>
    </scroll-view>
  </view>
</view>
