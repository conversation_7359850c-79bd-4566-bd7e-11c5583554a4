const config = require('../../config/index');
import { POST } from '../../library/optimizer/request';
Page({
  data: {
    adCompanies: [],
    searchQuery: '',
    page: 1,
    rows: 20,
    loading: false, // 添加 loading 状态，避免重复加载
    noMoreData: false, // 添加 noMoreData 状态，表示是否还有更多数据
    showNoMoreData: false,
    isSelectMode: false, // 是否是选择模式
  },

  onLoad(options) {
    // 根据传入参数决定页面模式
    if (options && options.mode === 'select') {
      this.setData({ isSelectMode: true });
    }
    this.getAdCompanies();
  },

  getAdCompanies() {
    if (this.data.loading || this.data.noMoreData) return;

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载中...',
    });

    const params = {
      findAdvComList: true,
      name: this.data.searchQuery,
      page: this.data.page,
      rows: this.data.rows,
    };

    POST(`/apiTsAdvComRegInSfaController.do?findAdvComList`, params, {
      custom: {
        isLoading: true,
        msg: '加载中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.hideLoading();
        this.setData({ loading: false });
        const newAdCompanies = res || [];
        if (newAdCompanies.length < this.data.rows) {
          this.setData({ noMoreData: true });
        }
        this.setData({
          adCompanies: this.data.page === 1 ? newAdCompanies : this.data.adCompanies.concat(newAdCompanies),
          page: this.data.page + 1,
        });
      })
      .catch((err) => {
        wx.hideLoading();
        this.setData({ loading: false });
        wx.showToast({
          title: err.head ? err.head.message : '查询失败',
          icon: 'none',
        });
      });
  },

  onSearchInput(e) {
    this.setData({
      searchQuery: e.detail.value,
    });
  },

  onSearch() {
    this.setData(
      {
        page: 1,
        adCompanies: [],
        noMoreData: false,
      },
      () => {
        this.getAdCompanies();
      },
    );
  },

  onClear() {
    this.setData(
      {
        searchQuery: '',
        page: 1,
        adCompanies: [],
        noMoreData: false,
      },
      () => {
        this.getAdCompanies();
      },
    );
  },

  selectAdCompany(e) {
    if (this.data.isSelectMode) {
      const selectedCompany = e.currentTarget.dataset.company;
      wx.setStorageSync('selectedAdCompany', selectedCompany);
      wx.navigateBack();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.noMoreData) {
      this.setData({
        showNoMoreData: true,
      });
      setTimeout(() => {
        this.setData({
          showNoMoreData: false,
        });
      }, 2000); // 2秒后隐藏提示
    } else {
      this.getAdCompanies();
    }
  },
  // 上拉触顶刷新事件
  onPullDownRefresh() {
    this.setData(
      {
        page: 1,
        adCompanies: [],
        noMoreData: false,
      },
      () => {
        this.getAdCompanies();
      },
    );
  },
});
