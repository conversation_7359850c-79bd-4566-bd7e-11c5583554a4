// app.js
import storage from './library/extend/storage.js';
import util from './library/extend/util.js';

const { default: logger } = require('./library/extend/log.js');
const { default: updateManager } = require('./library/manager/update_manager.js');
import { getDateToday } from './library/extend/timeFormat';
const config = require('./config/index');

import promisify from './library/optimizer/promisify.js';
const { default: debounce } = require('./library/optimizer/debounce.js');
const { default: throttle } = require('./library/optimizer/throttle.js');

const preloadAssetsData = require('./resource/preloadAssets/index.js');

Page = require('./library/extend/page.js').default; // 劫持page对象

App({
  globalData: {
    navigatorInfo: {},
    windowInfo: {},
    accountInfo: {},
    menuButtonInfo: {},
  },
  onLaunch(options) {
    console.log(options);

    // 检测运行环境
    this.checkEnvironment();

    this.preloadAssets();
    wx.$storage.setStorageSync('platform', wx.getDeviceInfo().platform);
  },

  // 检测运行环境
  checkEnvironment() {
    try {
      const systemInfo = wx.getAppBaseInfo();
      console.log('当前运行环境:', systemInfo);

      // 检查是否为非微信环境
      if (systemInfo.host.env !== 'WeChat') {
        console.log('检测到非微信环境:', systemInfo.host.env);

        // 延迟显示提示，确保小程序完全加载
        setTimeout(() => {
          wx.showModal({
            title: '环境提示',
            content: `为了获得最佳体验，建议在微信中打开使用。\n部分功能（如定位、视频上传等）可能受限。`,
            confirmText: '知道了',
            cancelText: '继续使用',
            success: (res) => {
              if (res.confirm) {
                // 用户选择"知道了"，可以在这里添加引导逻辑
                console.log('用户已知晓环境限制');
              } else {
                // 用户选择"继续使用"
                console.log('用户选择继续在当前环境使用');
              }
            }
          });
        }, 1000);
      } else {
        console.log('当前在微信环境中运行');
      }
    } catch (error) {
      console.log('环境检测失败:', error);
      // 如果检测失败，不影响正常使用
    }
  },

  // 获取环境名称
  getEnvironmentName(environment) {
    const envNames = {
      'qq': 'QQ',
      'alipay': '支付宝',
      'baidu': '百度',
      'toutiao': '抖音/今日头条',
      'wechat': '微信'
    };
    return envNames[environment] || '当前平台';
  },
  onShow() {
    global.state = 'show';
    updateManager.execute();
  },

  onHide() {
    global.state = 'hide';
  },

  onUnhandledRejection(res) {
    logger.error(res.reason);
  },
  onError(error) {
    logger.error(error);
  },
  onPageNotFound(res) {
    logger.error(res);
  },
  preloadAssets() {
    if (wx.canIUse('preloadAssets')) {
      wx.preloadAssets({
        data: preloadAssetsData.data,
        success(resp) {
          console.log('preloadAssets success', resp);
        },
        fail(err) {
          console.log('preloadAssets fail', err);
        },
      });
    }

    const mb = wx.getMenuButtonBoundingClientRect();
    const sh = wx.getWindowInfo().statusBarHeight;
    const distance = mb.top - sh;
    const navigator = {
      top: sh,
      bottom: sh + mb.height + distance * 2,
      left: 0,
      right: 0,
      height: mb.height + distance * 2,
      width: wx.getWindowInfo().screenWidth,
    };
    this.globalData.navigatorInfo = navigator;
    this.globalData.windowInfo = wx.getWindowInfo();
    this.globalData.accountInfo = wx.getAccountInfoSync();
    this.globalData.menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    wx.$storage = new storage();
    wx.$config = config;
    wx.$promisify = promisify;
    wx.$debounce = debounce;
    wx.$throttle = throttle;
    wx.$util = util;
  },
});
