@import '../miniprogram/resource/scss/index.scss';

.container {
  padding: 20px;
  background-color: #ffffff;
}

.logo {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.logo image {
  width: 100px;
  height: 100px;
}

.title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 5px;
}

.form-item {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #333;
}

t-input {
  width: 100%;
}

.verify-code-item {
  display: flex;
  align-items: center;
}

.verify-input {
  flex: 1;
}

.get-code-btn {
  margin-left: 10px;
  padding: 0 12px;
}

.password-input {
  display: flex;
  align-items: center;
}

.password-input t-input {
  flex: 1;
}

.password-input button {
  background: none;
  border: none;
  cursor: pointer;
}

.error-message {
  color: #ff4d4f; /* 设置文本颜色为红色 */
  font-size: 14px; /* 设置字体大小 */
  margin-top: 5px; /* 添加顶部间距 */
  padding: 2px 0; /* 上下填充 */
  line-height: 1.5; /* 设置行高 */
}

.no-more-data-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px 20px;
  border-radius: 5px;
  text-align: center;
  z-index: 1000;
}

.labelGroup {
  display: flex;
  align-items: baseline; /* 下标对齐 */
}
.redLab {
  color: rgb(247, 9, 9);
}
