/**
 * 一个代替wx.request的网络请求接口
 * 封装一些网络请求参数的设置，打破最大10个并发的限制，支持优先级设置
 */

// POST请求
function POST(url, params, config) {
  return request('POST', url, {
    params,
    config,
  });
}

// GET请求
function GET(url, params, config = {}) {
  return request('GET', url, {
    params,
    config,
  });
}

export default function request(method, url, { params, config }) {
  if (config.custom?.isLoading) {
    wx.showLoading({
      title: config.custom?.message || '加载中...',
      mask: true,
    });
  }
  let header = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (wx.$storage.getStorageSync('access_token')) header.Token = wx.$storage.getStorageSync('access_token');
  if (wx.$storage.getStorageSync('tby_token')) header.Authorization = wx.$storage.getStorageSync('tby_token');
  const brOutAddData = wx.$storage.getStorageSync('brOutAddData');
  if (!wx.$util.isEmpty(brOutAddData)) header.createVersion = brOutAddData.createVersion || '';
  header.SystemChannel = 'WECHAT';

  let Request = wxPromisify(wx.request, config);
  const accountInfo = getApp().globalData.accountInfo;
  const requestUrl = accountInfo.miniProgram.envVersion == 'release' ? wx.$config.prodURL : wx.$config.defaultURL;

  const defaultParams = {
    userName: wx.getStorageSync('userData')?.businessObject?.frUserName,
    userId: wx.getStorageSync('userData')?.businessObject?.userID,
    departCode: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.departCode,
    userType: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.userType,
    posId: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.posId,
    fullName: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.realName,
    orgCode: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.departCode,
    orgId: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.departId,
    orgName: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.departName,
    positionCode: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.posCode,
    positionId: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.posId,
    positionName: wx.getStorageSync('userData')?.businessObject?.userInfoEntity?.posName,
    appVersion: '40',
    appType: 'ANDROID',
    imei: 'null,null,null',
    phoneSend: '1',
    phoneFlag: 'sdk gphone64 x86 64',
    TOKEN_STRING_NAME: wx.getStorageSync('token'),
  };

  // Merge default parameters with provided parameters
  const finalParams = { ...defaultParams, ...params };

  return Request({
    enableCache: true,
    enableHttp2: true,
    enableQuic: true,
    header: header,
    url: `${requestUrl}${url}`,
    method: method,
    data: finalParams || {},
  });
}

function wxPromisify(fn, config) {
  return function (obj = {}) {
    return new Promise((resolve, reject) => {
      const custom = config?.custom;
      obj.success = function (res) {
        // 成功
        // console.log('接口名称')
        // console.log(obj.url)
        // console.log('接口参数')
        // console.log(obj.data)
        // console.log('接口数据')
        // console.log("----------",res)

        if (res.statusCode !== 200) {
          return reject(res);
        }

        if (custom?.isLoading) {
          wx.hideLoading();
        }
        const data = res.data;
        const { code, message } = data.head;

        if (code == 106) {
          let toastTitle = '请重新登录';
          if (!getApp().globalData.userLogining) {
            getApp().globalData.userLogining = true;
            wx.$util.toast(toastTitle).then((res) => {
              wx.removeStorageSync('userData');
              wx.removeStorageSync('token');
              wx.navigateTo({
                url: '/pages/login/index',
              });
            });
          }
          return;
        }

        // 不管成功与否都全部返回
        if (custom?.allReturn) {
          return resolve(data || {});
        }
        if (code != 100) {
          // 服务端返回的状态码不等于200，则reject()
          // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
          if (custom?.toast !== false) {
            wx.showToast({
              title: message,
              icon: 'none',
              mask: true,
              duration: 3000,
            });
          }
          // 如果需要返回接口所有数据 fullContent
          if (custom?.fullContent) {
            return resolve(data.businessObject || {});
          }
          // 如果需要catch返回，则进行reject
          if (custom?.catch) {
            return reject(data);
          } else {
            // 否则返回一个pending中的promise
            return new Promise(() => {});
          }
        }

        return resolve(data.businessObject || {});
      };
      obj.fail = function (err) {
        // 失败
        wx.hideLoading();
        reject(err);
      };
      obj.complete = function () {};
      fn(obj);
    });
  };
}

module.exports = {
  POST: POST,
  GET: GET,
};
