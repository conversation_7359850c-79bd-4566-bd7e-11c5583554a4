// 时间格式转换

// 时间戳转为 2022-01-01
export const timeStampToTime = (timeStamp) => {
  // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  if (timeStamp < 10000000000) {
    var date = new Date(timeStamp * 1000);
  } else {
    var date = new Date(timeStamp);
  }
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  return Y + M + D;
};

// 获取今天日期 2022-01-01
export const getTimeToday = () => {
  let date = new Date();
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  return Y + M + D;
};

// 2022-01-01 格式日期比较大小 date1 大于 date2 为 true
export const dateBiggerThen = (date1, date2) => {
  return Number(date1.replace(/-/g, '')) > Number(date2.replace(/-/g, ''));
};

// 获取今日年月日十分秒: 2023-01-20 10:30:26
export const getDateToday = () => {
  let date = new Date();
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  let H = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  let F = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  let S = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

  return Y + M + D + ' ' + H + F + S;
};
